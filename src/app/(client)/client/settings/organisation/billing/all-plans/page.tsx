"use client";
import React, { useContext, useState } from "react";
import SettingsLabel from "../../../components/settings-label";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  B<PERSON><PERSON><PERSON>bList,
  BreadcrumbSeparator,
} from "~/components/ui/breadcrumb";
import { Check, Loader2 } from "lucide-react";
import { Switch } from "~/components/ui/switch";
import Icons from "~/app/(client)/client/_components/billing/icons";
import { Button } from "~/components/ui/button";
import cogoToast from "cogo-toast";
import StorageOffloadModal from "../../../../_components/storage/storage-offload-modal";
import BillingCancellationModal from "../components/billing-cancellation-modal";
import {
  useGetSubscriptionPlans,
  subscriptionPlanUtils,
  useGetCurrentSubscription,
} from "~/utils/subscriptionPlans";
import { DeleteRequest, PostRequest, PutRequest } from "~/utils/new-request";
import { DataContext } from "~/store/GlobalState";

/* eslint-disable */

const Page = () => {
  const { subscriptionPlans, isLoading: plansLoading } =
    useGetSubscriptionPlans();
  const [currentPlan, setCurrentPlan] = useState<string>("Free");
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);
  const [isOffloadModalOpen, setIsOffloadModalOpen] = useState(false);
  const [isBillingCancellationModalOpen, setIsBillingCancellationModalOpen] =
    useState(false);
  const [
    alternateStorageCancellationModalOpen,
    setAlternateStorageCancellationModalOpen,
  ] = useState(false);
  const [isUnSubscribing, setIsUnSubscribing] = useState(false);

  const { currentSubscription } = useGetCurrentSubscription();

  const {
    state: {
      orgData: { id: orgId, email, subscription_plan_id },
    },
  } = useContext(DataContext);

  const { state } = useContext(DataContext);

  console.log('state ====>', state);

  const PLAN_HIERACHRY = ["Free", "Starter", "Business", "Enterprise"];

  const handleUpgrade = async (planName: string) => {
    setLoadingPlan(planName);

    try {
        if (subscription_plan_id === 'free') {
          await PostRequest(`/subscriptions/create`, {
            plan_name: planName,
            org_id: orgId || "",
            email,
          })
          .then((res) => {
            setCurrentPlan(planName);
            window.location.href = res?.data?.data?.checkout_session_url;
          })
          .catch((err) => {
            cogoToast.error(
              `${
                err.response?.data?.message || "An error occurred during upgrade."
              } Please try again.`,
              {
                position: "top-right",
                hideAfter: 4,
              }
            );
            setLoadingPlan(null);
          });
        }else{
          await PutRequest(`/subscriptions/modify`, {
            plan_name: planName,
            org_id: orgId || "",
          })
            .then((res) => {
              setCurrentPlan(planName);
              // window.location.href = res?.data?.data?.checkout_session_url;
            })
            .catch((err) => {
              cogoToast.error(
                `${
                  err.response?.data?.message || "An error occurred during upgrade."
                } Please try again.`,
                {
                  position: "top-right",
                  hideAfter: 4,
                }
              );
              setLoadingPlan(null);
            });
        }
      } catch (error) {
        cogoToast.error("An error occurred during upgrade. Please try again.", {
          position: "top-right",
          hideAfter: 4,
        });
        setLoadingPlan(null);
      } finally {
      }
  };

  // Show loading state while plans are being fetched
  if (plansLoading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <SettingsLabel />
        <div className="p-5 border-b">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/client/settings/organisation/billing">
                  Billing
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink className="text-black">
                  All Plans
                </BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-gray-500">
            Loading subscription plans...
          </span>
        </div>
      </div>
    );
  }

  if (!subscriptionPlans) {
    return (
      <div className="min-h-screen bg-gray-50">
        <SettingsLabel />
        <div className="p-5 border-b">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/client/settings/organisation/billing">
                  Billing
                </BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbLink className="text-black">
                  All Plans
                </BreadcrumbLink>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>
        </div>
        <div className="flex items-center justify-center py-12">
          <div className="text-red-500">
            Unable to load subscription plans. Please try again later.
          </div>
        </div>
      </div>
    );
  }

  // Sort plans for display
  const sortedPlans = subscriptionPlanUtils.sortPlansByPrice(subscriptionPlans);

  console.log("sortedPlan ===>", sortedPlans);
  console.log(
    "Telex ${currentSubscription?.name}",
    `Telex ${currentSubscription?.name}`
  );

  return (
    <div>
      <BillingCancellationModal
        isOpen={isBillingCancellationModalOpen}
        onClose={() => setIsBillingCancellationModalOpen(false)}
        onCancel={async (password, isAdmin) => {
            setIsUnSubscribing(true);
            await DeleteRequest(`/subscriptions/${orgId}`).then((res) => {
            if (res?.status === 200 || res?.status === 201) {
              cogoToast.success(res?.data?.message);
              window.location.href = "/client/settings/organisation/billing";
            }
            });
        }}
        isUnSubscribing={isUnSubscribing}
      />
      <StorageOffloadModal
        isOpen={isOffloadModalOpen}
        onClose={() => setIsOffloadModalOpen(false)}
      />
      <SettingsLabel />
      <div className="p-5 border-b">
        <Breadcrumb>
          <BreadcrumbList>
            <BreadcrumbItem>
              <BreadcrumbLink href="/client/settings/organisation/billing">
                Billing
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator />
            <BreadcrumbItem>
              <BreadcrumbLink className="text-black">All Plans</BreadcrumbLink>
            </BreadcrumbItem>
          </BreadcrumbList>
        </Breadcrumb>
      </div>
      <div className="w-full flex items-center justify-center mt-12">
        <div className="flex items-center gap-3">
          <p className="text-sm font-bold">Pay monthly</p>
          <Switch id="airplane-mode" />
          <div className="relative">
            <p className="text-sm font-bold text-[#667085]">Pay annually</p>
            <span className="absolute -top-6 -right-[110%]">
              <Icons name="billing-label" svgProps={{}} />
            </span>
          </div>
        </div>
      </div>
      <div className="flex grid grid-cols-1 md:grid-cols-3 gap-6 px-6 py-3 mx-auto">
        {sortedPlans.map((plan, index) => {
          const isCurrentPlan = plan.name === currentSubscription?.name;
          const isLoading = loadingPlan === plan.name;
          const planFeatures = subscriptionPlanUtils.getPlanFeatures(plan);
          const planAudience = subscriptionPlanUtils.getPlanAudience(plan);

          return (
            <div
              key={`${plan.name}-${index}`}
              className="group col-span-1 relative flex-1 p-2 rounded-3xl transition-all duration-100 border-4 border-white hover:box-content hover:border-[#BABAFB] hover:bg-[url('/images/pricing_bg.jpeg')] hover:bg-cover hover:bg-center"
            >
              <div
                className={`
                  relative rounded-2xl border overflow-hidden bg-[#F6F7F9] group-hover:bg-[#F6F7F9]/70  border-[#E6EAEF] transition-all duration-300 h-full
                  ${isCurrentPlan ? "border-purple-200" : ""}
                  group-hover:shadow-lg
                `}
              >
                <div className="flex items-start justify-between px-8 pt-8 bg-white">
                  <div className="">
                    <h3 className="text-xl font-bold text-[#101828]">
                      {plan.name}
                    </h3>
                    <p className="text-[#475467] text-sm">{planAudience}</p>
                  </div>
                  {isCurrentPlan && (
                    <div className="">
                      <div className="flex items-center gap-2 bg-gradient-to-b from-white to-[#F2EFFA] text-purple-700 px-3 py-2 border border-[#F1F1FE] rounded-full text-sm font-medium">
                        <Check size={16} />
                        Current Plan
                      </div>
                    </div>
                  )}
                </div>

                <div className=" py-8 px-8 bg-white">
                  <div className="flex items-center gap-2">
                    <span className="text-3xl font-bold text-black">
                      ${plan.fee}
                    </span>
                    <span className="text-[#475467]">per month</span>
                  </div>
                </div>

                <div className="">
                  <div className="bg-transparent px-8 py-5 ">
                    <h4 className="text-gray-900 font-medium ">Includes:</h4>
                  </div>
                  <ul className="space-y-4 px-6 py-6 bg-white">
                    {planFeatures.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-start gap-3">
                        <div className="flex-shrink-0 mt-0.5">
                          <Icons name="feature-bullets" svgProps={{}} />
                        </div>
                        <span className="text-gray-700 leading-relaxed text-sm">
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="mt-auto bg-[#F6F7F9] pt-8 px-6 pb-6 h-full">
                  {currentSubscription?.name !== "Free" && isCurrentPlan ? (
                    <Button
                      variant={"outline"}
                      className="w-fit py-6 px-6 bg-white border border-[#F81404] text-white rounded-xl font-medium transition-opacity duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-[#F81404] cursor-pointer"
                      onClick={() => {
                        setIsBillingCancellationModalOpen(true);
                      }}
                      disabled={isLoading || loadingPlan !== null}
                    >
                      {isLoading ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span>Cancelling...</span>
                        </div>
                      ) : (
                        "Cancel Subscription"
                      )}
                    </Button>
                  ) : PLAN_HIERACHRY.indexOf(currentSubscription?.name) >
                    PLAN_HIERACHRY.indexOf(plan.name) ? (
                    <div>
                      <Button
                        className="w-fit py-6 px-6 bg-white border border-[#7141F8] hover:opacity-90 text-white rounded-xl font-medium transition-opacity duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-[#7141F8] cursor-pointer"
                        onClick={() => {
                          handleUpgrade(plan.name);
                        }}
                        disabled={isLoading || loadingPlan !== null}
                      >
                        {isLoading ? (
                          <div className="flex items-center gap-2">
                            <Loader2 className="h-4 w-4 animate-spin" />
                            <span>Downgrading...</span>
                          </div>
                        ) : (
                          `Downgrade to Telex ${plan.name}`
                        )}
                      </Button>
                    </div>
                  ) : plan.fee > 0 &&
                    plan.name !== currentSubscription?.name ? (
                    <Button
                      className="w-fit py-6 px-6 bg-gradient-to-b from-[#8860f8] to-[#7141f8] hover:opacity-90 text-white rounded-xl font-medium transition-opacity duration-200 disabled:opacity-50 disabled:cursor-not-allowed cursor-pointer"
                      onClick={() => handleUpgrade(plan.name)}
                      disabled={isLoading || loadingPlan !== null}
                    >
                      {isLoading ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          <span>Upgrading...</span>
                        </div>
                      ) : (
                        `Upgrade to Telex ${plan.name}`
                      )}
                    </Button>
                  ) : null}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default Page;
