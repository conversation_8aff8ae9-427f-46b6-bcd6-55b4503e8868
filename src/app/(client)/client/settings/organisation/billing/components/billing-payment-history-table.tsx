import React from "react";
import {
  useReactTable,
  getCoreRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  ColumnDef,
  flexRender,
  SortingState,
  ColumnFiltersState,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "~/components/ui/table";
import PaymentHistoryEmptyState from "./billing-payment-history-empty-state";
import { Button } from "~/components/ui/button";
import Icons from "~/app/(client)/client/_components/billing/icons";
import { cn } from "~/lib/utils";
import { TelexTransaction, CreditTransactionRecord } from "../type";
import { SampleBillingPaymentHistory } from "../data";
import Image from "next/image";
import images from "~/assets/images";

interface BillingPaymentHistoryTableProps {
  transactionData?: CreditTransactionRecord[];
  isLoading?: boolean;
}

export default function BillingPaymentHistoryTable({
  transactionData = [],
  isLoading = false,
}: BillingPaymentHistoryTableProps) {
  const [sorting, setSorting] = React.useState<SortingState>([]);
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>(
    []
  );

  // Transform backend transaction data to display format
  const transformTransactionData = React.useCallback(
    (data: CreditTransactionRecord[]): TelexTransaction[] => {
      return data.map((record) => ({
        id: record.id,
        description: "AI Credit Usage",
        extraDescription: record.agent_name
          ? `by ${record.agent_name}`
          : `by @${record.user_name}`,
        type: record.type,
        servicePeriod: new Date(record.created_at).toLocaleDateString("en-US", {
          month: "long",
          day: "numeric",
          year: "numeric",
        }),
        date: new Date(record.created_at).toLocaleDateString("en-US", {
          year: "numeric",
          month: "2-digit",
          day: "2-digit",
        }),
        amount: {
          base: record.amount,
          vat: 0,
          vatRate: 0,
          total: record.amount,
        },
        paymentMethod: {
          type: "credit" as const,
          lastFour: "----",
          processor: "direct",
        },
        invoiceNumber: `TXN-${record.id.slice(-8)}`,
        status: "paid" as const,
        currency: "USD",
      }));
    },
    []
  );

  const columns: ColumnDef<TelexTransaction>[] = [
    {
      accessorKey: "description",
      header: () => {
        return <p className="text-[#667085]">Description</p>;
      },
      cell: ({ row }) => {
        return (
          <div className="flex flex-col items-start gap-0">
            <span className="text-gray-700">{row.original.description}</span>
            <span className="text-[#667085] text-sm">
              for {row.original.type}
            </span>
          </div>
        );
      },
    },
    {
      accessorKey: "date",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-medium hover:bg-transparent text-[#667085]"
          >
            Date
            <Icons name="move-down" svgProps={{}} />
          </Button>
        );
      },
      cell: ({ row }) => (
        <span className="text-gray-700">{row.getValue("date")}</span>
      ),
    },
    {
      accessorKey: "amount.total",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-medium hover:bg-transparent text-[#667085]"
          >
            Amount
            <Icons name="move-down" svgProps={{}} />
          </Button>
        );
      },
      cell: ({ row }) => {
        const amount = row.original.amount;

        return (
          <div className="flex flex-col items-start gap-0">
            <span className="text-gray-700">${amount.total.toFixed(2)}</span>
            {amount.vat > 0 ? (
              <span className="text-[#667085] text-sm">
                {`$${amount.base.toFixed(2)} + ($${amount.vat.toFixed(2)} VAT)`}
              </span>
            ) : (
              <span className="text-[#667085] text-sm">Credit Usage</span>
            )}
          </div>
        );
      },
    },
    {
      accessorKey: "paymentMethod.lastFour",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
            className="h-auto p-0 font-medium hover:bg-transparent text-[#667085]"
          >
            Payment Method
            <Icons name="move-down" svgProps={{}} />
          </Button>
        );
      },
      cell: ({ row }) => {
        const paymentMethod = row.original.paymentMethod;

        // if (paymentMethod.type === "credit") {
        //   return (
        //     <div className="flex items-center gap-3">
        //       <div className="border rounded bg-blue-50 border-blue-200 py-2 px-3 text-blue-600 text-sm font-medium">
        //         Credit
        //       </div>
        //       <span className="text-gray-700 text-sm">Internal Transaction</span>
        //     </div>
        //   );
        // }

        return (
          <div className="flex items-center gap-3">
            <Image
              src={images.visaLogo}
              alt={""}
              width={50}
              height={50}
              className={cn(
                `border rounded bg-white border-[#F5F5F5] py-2 px-2`
              )}
            />
            <div className="flex items-center gap-1">
              <span>••••</span>
              <span className="text-gray-700">{paymentMethod.lastFour}</span>
              <span className="text-[#667085] text-sm">{`(${paymentMethod.processor})`}</span>
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: "action",
      header: () => {
        return (
          <div className="h-auto p-0 font-medium hover:bg-transparent text-[#667085]">
            Actions
          </div>
        );
      },
      cell: ({ row }) => (
        <Button
          onClick={() =>
            window.open(`/billing/invoice/${row.original.id}`, "_blank")
          }
          variant={"outline"}
          className="text-gray-700 h-fit p-1"
        >
          <Image src={images.fileDoc} alt={""} width={25} height={25} />
        </Button>
      ),
    },
  ];

  const tableData = React.useMemo(() => {
    // Use real transaction data if available, otherwise fall back to sample data
    if (transactionData && transactionData.length > 0) {
      return transformTransactionData(transactionData);
    }

    // If transactionData is null or empty array, show empty state
    if (transactionData !== undefined) {
      return [];
    }

    // Fallback to sample data for development/testing
    return SampleBillingPaymentHistory;
  }, [transactionData, transformTransactionData]);

  const table = useReactTable({
    data: tableData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    state: {
      sorting,
      columnFilters,
    },
    initialState: {
      pagination: {
        pageSize: 10,
      },
    },

    autoResetPageIndex: false,
  });

  const currentPage = table.getState().pagination.pageIndex + 1;
  const totalPages = table.getPageCount();
  const hasData = tableData.length > 0;

  // Show loading state
  if (isLoading) {
    return (
      <div className="w-full mx-auto bg-white border mt-6 rounded-xl overflow-hidden">
        <div className="flex items-center justify-center py-16">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-gray-500">Loading payment history...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full mx-auto  bg-white border  mt-6 rounded-xl overflow-hidden">
      {hasData ? (
        <>
          <div className="border-b">
            <Table>
              <TableHeader>
                {table.getHeaderGroups().map((headerGroup) => (
                  <TableRow key={headerGroup.id} className="bg-gray-50">
                    {headerGroup.headers.map((header) => (
                      <TableHead
                        key={header.id}
                        className="font-medium text-gray-700"
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    ))}
                  </TableRow>
                ))}
              </TableHeader>
              <TableBody>
                {table.getRowModel().rows?.length ? (
                  table.getRowModel().rows.map((row) => (
                    <TableRow
                      key={row.id}
                      data-state={row.getIsSelected() && "selected"}
                      className="hover:bg-gray-50"
                    >
                      {row.getVisibleCells().map((cell) => (
                        <TableCell key={cell.id} className="py-4">
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell
                      colSpan={columns.length}
                      className="h-24 text-center"
                    >
                      No results.
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          <div className="flex items-center justify-between px-6 space-x-2 py-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
              className="flex items-center gap-2 border-gray-300"
            >
              <Icons name="move-left" svgProps={{}} />
              Previous
            </Button>

            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(totalPages, 7) }, (_, i) => {
                let pageNum: number;
                if (totalPages <= 7) {
                  pageNum = i + 1;
                } else if (currentPage <= 4) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 3) {
                  pageNum = totalPages - 6 + i;
                } else {
                  pageNum = currentPage - 3 + i;
                }

                if (pageNum === 4 && currentPage > 4 && totalPages > 7) {
                  return (
                    <span key="ellipsis1" className="px-2">
                      ...
                    </span>
                  );
                }
                if (
                  pageNum === totalPages - 3 &&
                  currentPage < totalPages - 3 &&
                  totalPages > 7
                ) {
                  return (
                    <span key="ellipsis2" className="px-2">
                      ...
                    </span>
                  );
                }

                return (
                  <Button
                    key={pageNum}
                    variant={currentPage === pageNum ? "default" : "outline"}
                    size="sm"
                    onClick={() => table.setPageIndex(pageNum - 1)}
                    className="w-8 h-8 p-0"
                  >
                    {pageNum}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
              className="flex items-center gap-2 border-gray-300"
            >
              Next
              <Icons name="move-right" svgProps={{}} />
            </Button>
          </div>
        </>
      ) : (
        <PaymentHistoryEmptyState />
      )}
    </div>
  );
}
