"use client";
import { useContext, useState } from "react";
import { Edit2Icon } from "lucide-react";
import SettingsLabel from "../../components/settings-label";
import DeleteAccount from "./components/delete-account";
import EditProfileDialog from "../../../_components/edit-profile-modal";
import { DataContext } from "~/store/GlobalState";
import images from "~/assets/images";

const AccountPage = () => {
  const [profileDialog, setProfileDialog] = useState(false);
  const { state } = useContext(DataContext);
  const { user } = state;

  return (
    <div className="">
      <SettingsLabel />
      <div className="p-4">
        <div className="mb-4">
          <h1 className="text-base font-semibold">Your Account Information</h1>
          <p className="text-sm text-[#344054]">
            Manage your account data with ease.
          </p>
        </div>

        <div className="flex items-start gap-4">
          {/* first profile */}
          <div className="border rounded-xl p-4 flex-1 relative">
            <div className="border absolute top-4 right-4 p-2 rounded-md cursor-pointer">
              <Edit2Icon
                className="w-5 h-5 "
                onClick={() => setProfileDialog(true)}
              />
            </div>
            <div className="h-36 w-36 border rounded-xl bg-[#F2F4F7] flex items-center justify-center">
              <img
                src={user?.avatar_url || images?.user}
                alt="account logo"
                className="w-full h-full object-cover rounded-xl"
              />
            </div>
            <div className="flex flex-col gap-4 mt-4">
              <div className="space-y-2">
                <h3 className="text-sm text-[#475467]">Name</h3>
                <div className="flex items-center gap-2">
                  <p>{user?.full_name}</p>
                  <div className="w-2 h-2 rounded-full bg-[#D9D9D9]"></div>
                  <span className="text-[#667085]">
                    {user?.display_name && "@"}
                    {user?.display_name}
                  </span>
                </div>
              </div>

              {user?.title && (
                <div className="space-y-2">
                  <h3 className="text-sm text-[#475467]">Title</h3>
                  <p>{user?.title}</p>
                </div>
              )}

              {user?.timezone && (
                <div className="space-y-2">
                  <h3 className="text-sm text-[#475467]">Time Zone</h3>
                  <p>{user?.timezone}</p>
                </div>
              )}
            </div>
          </div>

          {/* Email and number section */}
          <div className="border rounded-xl p-4 flex-1 flex flex-col gap-4">
            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <h3 className="text-sm text-[#475467]">Email Address</h3>
                <p>{user?.email}</p>
              </div>
              {/* <div className="border p-2 rounded-md cursor-pointer">
                <Edit2Icon
                  className="w-5 h-5"
                  onClick={() => setEmailDialog(true)}
                />
              </div> */}
            </div>

            <div className="flex items-center justify-between">
              <div className="space-y-2">
                <h3 className="text-sm text-[#475467]">Phone Number</h3>
                <p>{user?.phone}</p>
              </div>
              {/* <div className="border p-2 rounded-md cursor-pointer">
                <Edit2Icon
                  className="w-5 h-5"
                  onClick={() => setNumberDialog(true)}
                />
              </div> */}
            </div>
          </div>
        </div>

        <DeleteAccount />
      </div>

      <EditProfileDialog
        isOpen={profileDialog}
        onClose={() => setProfileDialog(false)}
      />
    </div>
  );
};

export default AccountPage;
