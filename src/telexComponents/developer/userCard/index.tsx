import { ChevronDown } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { Avatar, AvatarFallback, AvatarImage } from "~/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuShortcut,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useUserData } from "~/hooks/use-user-data";
import { cn } from "~/lib/utils";

const UserCard = () => {
  const { user } = useUserData();
  const router = useRouter();

  const [avatarKey, setAvatarKey] = useState(Date.now());

  useEffect(() => {
    setAvatarKey(Date.now());
  }, [user]);

  const getAvatarUrl = () => {
    if (!user?.avatar_url) return "";
    const separator = user.avatar_url.includes("?") ? "&" : "?";
    return `${user.avatar_url}${separator}t=${avatarKey}`;
  };

  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((part) => part[0])
      .join("")
      .toUpperCase()
      .slice(0, 2);
  };

  const handleLogout = async () => {
    router.push("/developer/login");
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <button
          type="button"
          className="flex items-center rounded-full p-1 hover:bg-subtle"
        >
          {user && (
            <div className="border rounded-full">
              <Avatar className="size-8 sm:size-10">
                <AvatarImage src={getAvatarUrl()} />
                <AvatarFallback className="bg-primary/30 uppercase">
                  {getInitials(user.full_name || user.username || "")}
                </AvatarFallback>
              </Avatar>
            </div>
          )}

          <ChevronDown
            data-testid="chevronDown"
            className={cn("size-4 text-neutral-dark-2 sm:size-5")}
          />
        </button>
      </DropdownMenuTrigger>

      <DropdownMenuContent className="mr-1 w-56" align="end">
        <DropdownMenuLabel className="pb-0 pt-3 text-center">
          {user?.full_name}
        </DropdownMenuLabel>
        <span className="block px-2 pb-1 text-xs text-neutral-dark-1 text-center">
          {user?.email ?? "Signed In"}
        </span>
        <DropdownMenuSeparator />

        <DropdownMenuSeparator />
        <DropdownMenuItem onClick={handleLogout} className="cursor-pointer">
          <span className="font-medium">Log out</span>
          <DropdownMenuShortcut>⇧Q</DropdownMenuShortcut>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default UserCard;
