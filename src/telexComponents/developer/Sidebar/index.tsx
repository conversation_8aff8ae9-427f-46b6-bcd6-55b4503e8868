"use client";

import { Home, CircleX } from "lucide-react";
import React, { useContext } from "react";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { DataContext } from "~/store/GlobalState";
import { ACTIONS } from "~/store/Actions";

interface ButtonData {
  id: string;
  icon: React.ComponentType<any>;
  label: string;
  link: string;
}

const SideBar: React.FC = () => {
  const pathname = usePathname();
  const { state, dispatch } = useContext(DataContext);

  const buttonData: ButtonData[] = [
    {
      id: "home",
      icon: Home,
      label: "Dashboard",
      link: "/developer/dashboard",
    },
  ];

  return (
    <div
      className={`fixed top-0 z-50 md:z-0 md:translate-x-0 transition-transform duration-300 ease-in-out
               ${state?.openSidebar === true ? "translate-x-0" : "-translate-x-40"}`}
    >
      <div
        className={`h-[100vh] w-[110px] p-[8px] -z-50
        md:flex flex-col justify-start`}
      >
        <div className="h-full w-full text-[#344054] rounded-[12px] z-auto bg-[#f2f4f7] flex flex-col justify-between">
          <div className="relative">
            {
              <CircleX
                className="md:hidden absolute right-0 top-0"
                onClick={() =>
                  dispatch({ type: ACTIONS.OPEN_SIDEBAR, payload: false })
                }
              />
            }

            <div
              className="flex flex-col items-center mt-6 w-full text-[12px]"
              onClick={() =>
                dispatch({ type: ACTIONS.OPEN_SIDEBAR, payload: false })
              }
            >
              {buttonData.map((button) => {
                const isActive =
                  button.id === "home"
                    ? pathname === "/developer/dashboard"
                    : pathname.includes(button.id);
                return (
                  <Link
                    href={button.link}
                    key={button.id}
                    className={`flex flex-col group mb-2 items-center justify-center space-y-1 w-full p-[5px] ${
                      isActive
                        ? "font-semibold scale-[1.1]"
                        : "hover:font-semibold "
                    } ${
                      pathname.includes("/developer/dashboard")
                        ? "pointer-events-none cursor-not-allowed"
                        : ""
                    }`}
                  >
                    <span
                      className={`p-[8px] rounded-[7px]  ${
                        isActive ? "bg-[#7b50fb]" : "group-hover:bg-[#7b50fb]"
                      }`}
                    >
                      <button.icon
                        strokeWidth={1.2}
                        className={`${
                          isActive
                            ? "fill-white stroke-[#7b50fb]"
                            : "group-hover:fill-white group-hover:stroke-[#7b50fb]"
                        }`}
                      />
                    </span>
                    <span>{button.label}</span>
                  </Link>
                );
              })}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SideBar;
